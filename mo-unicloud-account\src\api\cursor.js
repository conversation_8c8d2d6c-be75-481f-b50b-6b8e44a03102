import http from "@/utils/http"

export const addAccountAPI = params => {
  return http.post("/addAccountCursor", { params })
}

export const deleteAccountAPI = params => {
  return http.post("/deleteAccountCursor", { params })
}

export const updateAccountAPI = params => {
  return http.post("/updateAccountCursor", { params })
}

export const getAccountListAPI = params => {
  return http.post("/getAccountCursorList", { params })
}
