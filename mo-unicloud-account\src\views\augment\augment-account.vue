<template>
  <div style="padding: 10px">
    <el-button
      style="margin-bottom: 20px"
      size="large"
      :icon="Plus"
      type="success"
      @click="openAddDialog"
    >
      添加 Cursor 账号
    </el-button>

    <el-table v-loading="loading" :data="tableData" style="width: 100%" border stripe>
      <el-table-column type="index" prop="序号" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column prop="token" label="Token" show-overflow-tooltip />
      <el-table-column prop="create_time" label="过期时间">
        <template #default="{ row }">
          {{ row }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200px" align="center" fixed="right">
        <template #default="{ row }">
          <el-button plain :icon="Edit" type="warning" @click="openEditDialog(row)">编辑</el-button>
          <el-button plain :icon="Delete" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="margin-top: 20px; display: flex; justify-content: center">
      <el-pagination
        background
        v-model:currentPage="pageParams.pageNum"
        v-model:page-size="pageParams.pageSize"
        :total="total"
        :pager-count="7"
        @current-change="currentChange"
        @size-change="sizeChange"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
      />
    </div>
  </div>

  <el-dialog v-model="dialogParam.visible" :title="dialogParam.title" width="650px">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="form.email" placeholder="请输入邮箱" />
      </el-form-item>
      <el-form-item label="Token" prop="token">
        <el-input
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 4 }"
          v-model="form.token"
          placeholder="请输入Token"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex; justify-content: flex-end">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="submitForm">
          {{ dialogParam.type === "edit" ? "更新" : "添加" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { markRaw, ref, onMounted } from "vue"
import { ElMessage, ElMessageBox } from "element-plus"
import { Plus, Delete, Edit } from "@element-plus/icons-vue"
import dayjs from "dayjs"

const tableData = ref([])
const loading = ref(false)
const dialogParam = ref({
  visible: false,
  type: "add",
  title: "添加账户"
})
const currentData = ref({})
const formRef = ref(null)
const form = ref({
  _id: "",
  email: "",
  token: "",
  create_time: ""
})
const rules = markRaw({
  email: [
    { required: true, message: "请输入邮箱地址", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }
  ],
  token: [
    { required: true, message: "请输入Token", trigger: "blur" },
    { min: 10, message: "Token长度不能少于10个字符", trigger: "blur" }
  ]
})
const total = ref(0)
const pageParams = ref({
  pageNum: 1,
  pageSize: 10
})

onMounted(() => {
  getDataList()
})

const getDataList = async () => {
  loading.value = true
  setTimeout(() => {
    tableData.value = [
      {
        _id: "64a1b2c3d4e5f6789012345a",
        email: "<EMAIL>",
        token: "sk-1234567890abcdef",
        create_time: "2024-01-15"
      },
      {
        _id: "64a1b2c3d4e5f6789012345b",
        email: "<EMAIL>",
        token: "sk-abcdef1234567890",
        create_time: "2024-01-16"
      },
      {
        _id: "64a1b2c3d4e5f6789012345c",
        email: "<EMAIL>",
        token: "sk-fedcba0987654321",
        create_time: "2024-01-17"
      }
    ]
    total.value = tableData.value.length
    loading.value = false
  }, 500)
}

const currentChange = current => {
  pageParams.value.pageNum = current
  getDataList()
}
const sizeChange = size => {
  pageParams.value.pageNum = 1
  pageParams.value.pageSize = size
  getDataList()
}

// 重置表单
const resetForm = () => {
  form.value._id = ""
  form.value.email = ""
  form.value.token = ""
  form.value.create_time = ""
  currentData.value = {}
  formRef.value?.clearValidate()
}

// 添加
const openAddDialog = () => {
  resetForm()
  dialogParam.value = {
    visible: true,
    type: "add",
    title: "添加账户"
  }
}

// 修改
const openEditDialog = row => {
  resetForm()
  currentData.value = { ...row }
  Object.assign(form.value, row)
  dialogParam.value = {
    visible: true,
    type: "edit",
    title: "编辑账户"
  }
}

// 提交添加和修改
const submitForm = async () => {
  formRef.value?.validate(async valid => {
    if (valid) {
      if (dialogParam.value.type === "edit") {
        console.log("修改账户", form.value)
        ElMessage.success("修改成功")
      } else {
        // 添加时自动生成ID和创建时间
        form.value._id = generateId()
        form.value.create_time = getCurrentTime()
        console.log("添加账户", form.value)
        ElMessage.success("添加成功")
      }
      dialogParam.value.visible = false
      getDataList() // 刷新列表
    }
  })
}

// 生成随机ID
const generateId = () => {
  return Math.random().toString(36).substring(2, 11) + Date.now().toString(36)
}

// 获取当前时间
const getCurrentTime = () => {
  return dayjs().format("YYYY-MM-DD")
}

// 确认删除
const handleDelete = row => {
  ElMessageBox.confirm(`确定要删除邮箱为 ${row.email} 的账户吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      console.log("删除账户", row)
      ElMessage.success("删除成功")
      getDataList() // 刷新列表
    })
    .catch(() => {
      console.log("取消删除")
    })
}

const handleCancel = async () => {
  dialogParam.value.visible = false
}
</script>

<style lang="scss" scoped></style>
