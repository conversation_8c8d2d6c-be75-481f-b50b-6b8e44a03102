<template>
  <div style="padding: 10px">
    <el-button
      style="margin-bottom: 20px"
      plain
      size="large"
      :icon="Plus"
      type="primary"
      @click="openAddDialog"
    >
      添加 Augment 账号
    </el-button>

    <el-table v-loading="loading" :data="tableData" style="width: 100%" border stripe>
      <el-table-column type="index" label="序号" width="60px" align="center" />
      <el-table-column prop="email" label="邮箱">
        <template #default="{ row }">
          <div style="display: flex; align-items: center">
            <span>{{ row.email }}</span>
            <CopyButton :text="row.email" success-message="邮箱复制成功" />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="email" label="创建时间" width="140" align="center">
        <template #default="{ row }">
          <el-tag :type="getExpireTagType(row.create_time)">
            {{ formatTime(row.create_time) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200px" align="center" fixed="right">
        <template #default="{ row }">
          <el-button plain :icon="Edit" type="warning" @click="openEditDialog(row)">编辑</el-button>
          <el-button plain :icon="Delete" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="margin-top: 20px; display: flex; justify-content: center">
      <el-pagination
        background
        v-model:currentPage="pageParams.pageNum"
        v-model:page-size="pageParams.pageSize"
        :total="total"
        :pager-count="7"
        @current-change="currentChange"
        @size-change="sizeChange"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
      />
    </div>
  </div>

  <el-dialog v-model="dialogParam.visible" :title="dialogParam.title" width="500px">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="60px">
      <el-form-item label="邮 箱" prop="email">
        <el-input v-model="form.email" placeholder="请输入邮箱" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex; justify-content: flex-end">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="submitForm">
          {{ dialogParam.type === "edit" ? "更新" : "添加" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { markRaw, ref, onMounted } from "vue"
import { ElMessage, ElMessageBox } from "element-plus"
import { Plus, Delete, Edit } from "@element-plus/icons-vue"
import { getAccountListAPI, addAccountAPI, deleteAccountAPI, updateAccountAPI } from "@/api/augment"
import CopyButton from "@/components/CopyButton.vue"
import dayjs from "dayjs"

const tableData = ref([])
const loading = ref(false)
const dialogParam = ref({
  visible: false,
  type: "add",
  title: "添加账户"
})
const currentData = ref({})
const formRef = ref(null)
const form = ref({
  _id: "",
  email: ""
})
const rules = markRaw({
  email: [
    { required: true, message: "请输入邮箱地址", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }
  ]
})
const total = ref(0)
const pageParams = ref({
  pageNum: 1,
  pageSize: 10
})

onMounted(() => {
  getDataList()
})

const getDataList = async () => {
  loading.value = true

  const res = await getAccountListAPI(pageParams.value)
  if (res.errCode === 0) {
    total.value = res.count
    tableData.value = res.data
    loading.value = false
  }
}

const currentChange = current => {
  pageParams.value.pageNum = current
  getDataList()
}
const sizeChange = size => {
  pageParams.value.pageNum = 1
  pageParams.value.pageSize = size
  getDataList()
}

// 重置表单
const resetForm = () => {
  form.value._id = ""
  form.value.email = ""
  currentData.value = {}
  formRef.value?.clearValidate()
}

// 添加
const openAddDialog = () => {
  resetForm()
  dialogParam.value = {
    visible: true,
    type: "add",
    title: "添加账户"
  }
}

// 修改
const openEditDialog = row => {
  resetForm()
  currentData.value = { ...row }
  Object.assign(form.value, row)
  dialogParam.value = {
    visible: true,
    type: "edit",
    title: "编辑账户"
  }
}

// 提交添加和修改
const submitForm = async () => {
  formRef.value?.validate(async valid => {
    if (valid) {
      if (dialogParam.value.type === "edit") {
        const res = await updateAccountAPI(form.value)
        if (res.errCode === 0) {
          ElMessage.success("修改成功")
          getDataList()
          dialogParam.value.visible = false
        }
      } else {
        const res = await addAccountAPI(form.value)
        console.log(res)
        if (res.errCode === 0) {
          ElMessage.success("添加成功")
          getDataList()
          dialogParam.value.visible = false
        }
      }
    }
  })
}

// 确认删除
const handleDelete = row => {
  ElMessageBox.confirm(`确定要删除邮箱为 ${row.email} 的账户吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      const res = await deleteAccountAPI(row)
      if (res.errCode === 0) {
        ElMessage.success("删除成功")
        getDataList() // 刷新列表
      }
    })
    .catch(() => {
      console.log("取消删除")
    })
}

const formatTime = time => {
  return dayjs(time).format("YYYY-MM-DD")
}

const handleCancel = async () => {
  dialogParam.value.visible = false
}
</script>

<style lang="scss" scoped></style>
