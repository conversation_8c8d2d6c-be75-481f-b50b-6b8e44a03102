<template>
  <el-container style="height: 100%">
    <el-aside width="200px">
      <el-menu
        active-text-color="#ffd04b"
        background-color="#545c64"
        text-color="#fff"
        style="height: 100%"
        :default-active="route.path"
        @select="handleSelect"
      >
        <el-menu-item v-for="menu in menus" :key="menu.path" :index="menu.path">
          <el-icon><Setting /></el-icon>
          <span>{{ menu.name }}</span>
        </el-menu-item>
      </el-menu>
    </el-aside>
    <el-main>
      <el-card shadow="always" :body-style="{ padding: '20px' }">
        <router-view></router-view>
      </el-card>
    </el-main>
  </el-container>
</template>

<script setup>
import { Setting } from "@element-plus/icons-vue"
import { useRoute, useRouter } from "vue-router"

const route = useRoute()
const router = useRouter()
const menus = [
  {
    path: "/cursor",
    name: "<PERSON>ursor"
  },
  {
    path: "/augment",
    name: "Augment"
  }
]

const handleSelect = path => {
  router.push(path)
}
</script>

<style lang="scss" scoped></style>
