import axios from "axios"

const http = axios.create({
  baseURL: process.env.VITE_BASE_URL,
  timeout: 10000
})

http.interceptors.request.use(
  config => {
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

http.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.log(error.response)
    return Promise.reject(error)
  }
)

export default http
