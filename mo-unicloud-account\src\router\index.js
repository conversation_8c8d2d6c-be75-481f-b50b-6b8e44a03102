import { createWebHashHistory } from "vue-router"
import { createRouter } from "vue-router"

const routes = [
  {
    path: "/",
    redirect: "/cursor"
  },
  {
    path: "/cursor",
    name: "Cursor",
    component: () => import("@/views/cursor/cursor-account.vue")
  },
  {
    path: "/augment",
    name: "Augment",
    component: () => import("@/views/augment/augment-account.vue")
  }
]

const router = createRouter({
  routes,
  history: createWebHashHistory(),
  scrollBehavior: () => ({ left: 0, top: 0, behavior: "smooth" })
})

export default router
