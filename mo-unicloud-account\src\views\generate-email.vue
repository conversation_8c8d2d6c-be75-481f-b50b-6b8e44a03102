<template>
  <div class="email-generator">
    <div class="container">
      <h1>邮箱生成器</h1>

      <el-form :model="form" label-width="100px">
        <el-form-item label="邮箱后缀:">
          <el-input
            v-model="form.emailSuffix"
            placeholder="例如: gmail.com"
            clearable
          />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="generateEmails" 
            :loading="generating"
            style="width: 100%"
            size="large"
          >
            生成邮箱
          </el-button>
        </el-form-item>
      </el-form>

      <div class="result" :class="{ 'has-content': currentEmail }">
        <div class="result-text">
          {{ currentEmail || '点击按钮生成邮箱...' }}
        </div>
        <el-button
          v-if="currentEmail"
          type="success"
          size="small"
          @click="copyEmail"
          :loading="copying"
        >
          {{ copyButtonText }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import Mock from 'mockjs'

const form = reactive({
  emailSuffix: 'momomomowei.icu'
})

const currentEmail = ref('')
const generating = ref(false)
const copying = ref(false)
const copyButtonText = ref('复制')

const generateEmails = async () => {
  generating.value = true
  
  // 模拟生成延迟，增加用户体验
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const suffix = form.emailSuffix || 'momomomowei.icu'
  
  const data = Mock.mock({
    email: function () {
      const firstName = Mock.mock('@first').toLowerCase()
      const lastName = Mock.mock('@last').toLowerCase()
      return `${firstName}_${lastName}@${suffix}`
    }
  })
  
  currentEmail.value = data.email
  generating.value = false
  
  ElMessage.success('邮箱生成成功！')
}

const copyEmail = async () => {
  if (!currentEmail.value) return
  
  copying.value = true
  
  try {
    await navigator.clipboard.writeText(currentEmail.value)
    copyButtonText.value = '已复制!'
    ElMessage.success('邮箱已复制到剪贴板')
  } catch (err) {
    // 降级方案：使用传统方法
    const textArea = document.createElement('textarea')
    textArea.value = currentEmail.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    
    copyButtonText.value = '已复制!'
    ElMessage.success('邮箱已复制到剪贴板')
  }
  
  copying.value = false
  
  // 2秒后重置按钮文本
  setTimeout(() => {
    copyButtonText.value = '复制'
  }, 2000)
}
</script>

<style lang="scss" scoped>
.email-generator {
  padding: 50px 20px;
  background: #f5f5f5;
  min-height: 100vh;
  
  .container {
    background: white;
    padding: 40px;
    max-width: 600px;
    margin: 0 auto;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    
    h1 {
      text-align: center;
      margin-bottom: 30px;
      font-size: 1.8rem;
      color: #333;
      font-weight: 600;
    }
  }
  
  .result {
    background: #f8f9fa;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;
    margin-top: 20px;
    transition: all 0.3s ease;
    
    &.has-content {
      border-color: #409eff;
      background: #ecf5ff;
    }
    
    .result-text {
      flex: 1;
      font-family: 'Courier New', monospace;
      font-size: 1.1rem;
      color: #333;
      word-break: break-all;
      margin-right: 15px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .email-generator {
    padding: 20px 10px;
    
    .container {
      padding: 20px;
      
      h1 {
        font-size: 1.5rem;
      }
    }
    
    .result {
      flex-direction: column;
      gap: 15px;
      
      .result-text {
        margin-right: 0;
        text-align: center;
      }
    }
  }
}
</style>
